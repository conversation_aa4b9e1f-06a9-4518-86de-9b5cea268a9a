import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDraftEditor } from '@/hooks/internal/useDraftEditor';
import { useStepNavigation } from './useStepNavigation';
import { useSubmitDialog } from './useSubmitDialog';
import { useSolutionEditor } from './useSolutionEditor';
import { UseDraftEditorPageReturn } from '../types';
import { rejectDraftAsEditor } from '@/services/internal/editorService';
import { useToast } from '@/hooks/use-toast';

export function useDraftEditorPage(draftPublicId: string, learningNodePublicId: string): UseDraftEditorPageReturn {
  const router = useRouter();
  const { toast } = useToast();
  
  // Rejection state
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isRejecting, setIsRejecting] = useState(false);
  
  // Get draft editor data and methods
  const {
    draft,
    isLoading,
    isSaving,
    isSubmitting,
    isDirty,
    error,
    canSubmit,
    saveStatus,
    autoSaveStatus,
    save,
    submit,
    updateExerciseData,
    updateSolutionData,
    updateDifficulty,
  } = useDraftEditor(draftPublicId, learningNodePublicId);

  // Wrap save to match expected type
  const wrappedSave = async () => {
    await save();
  };

  // Wrap updateDifficulty to match expected type
  const wrappedUpdateDifficulty = (difficulty: string) => {
    if (difficulty === 'low' || difficulty === 'medium' || difficulty === 'high') {
      updateDifficulty(difficulty);
    }
  };

  // Step navigation
  const stepNavigation = useStepNavigation({
    draft,
    isDirty,
    save: wrappedSave
  });

  // Submit dialog
  const submitDialog = useSubmitDialog({ submit });

  // Solution editor
  const solutionEditor = useSolutionEditor({
    draft,
    updateSolutionData
  });
  
  // Handle draft rejection
  const handleReject = async () => {
    if (!draft || !rejectReason.trim() || rejectReason.trim().length < 10) return;
    
    setIsRejecting(true);
    try {
      const result = await rejectDraftAsEditor(draft.id, {
        rejectionReason: rejectReason.trim()
      });
      
      if (result.status === 'success') {
        toast({
          title: 'Draft rejected',
          description: 'The draft has been rejected with your feedback.',
        });
        setShowRejectModal(false);
        setRejectReason('');
        // Navigate back to the learning node page
        router.push(`/internal/editor/${learningNodeId}`);
      } else {
        throw new Error(result.message || 'Failed to reject draft');
      }
    } catch (error) {
      console.error('Failed to reject draft:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reject draft',
        variant: 'destructive',
      });
    } finally {
      setIsRejecting(false);
    }
  };
  
  // Determine if editor can reject this draft
  const canReject = draft && (
    draft.status === 'NEW' || 
    draft.status === 'IN_REVIEW' ||
    draft.status === 'REJECTED_BY_ADMIN' ||
    draft.status === 'REJECTED_BY_EDITOR'
  );

  return {
    // Draft data
    draft,
    isLoading,
    error: error || undefined,
    
    // Save/Submit state
    isSaving,
    isSubmitting,
    isDirty,
    canSubmit: canSubmit || false,
    saveStatus,
    autoSaveStatus,
    
    // Actions
    save: wrappedSave,
    submit,
    updateExerciseData,
    updateSolutionData,
    updateDifficulty: wrappedUpdateDifficulty,
    
    // Rejection state and actions
    canReject: !!canReject,
    showRejectModal,
    setShowRejectModal,
    rejectReason,
    setRejectReason,
    isRejecting,
    handleReject,
    
    // Composed hooks
    stepNavigation,
    submitDialog,
    solutionEditor,
  };
}